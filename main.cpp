#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <climits>

using namespace std;

struct Server {
    int npus;
    int speed_coeff;
    int memory;
};

struct User {
    int start_time;
    int end_time;
    int sample_count;
};

struct Request {
    int time;
    int server;
    int npu;
    int batchsize;
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    
    int N;
    cin >> N;
    
    vector<Server> servers(N);
    for (int i = 0; i < N; i++) {
        cin >> servers[i].npus >> servers[i].speed_coeff >> servers[i].memory;
    }
    
    int M;
    cin >> M;
    
    vector<User> users(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].start_time >> users[i].end_time >> users[i].sample_count;
    }
    
    vector<vector<int>> latency(N, vector<int>(M));
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> latency[i][j];
        }
    }
    
    int a, b;
    cin >> a >> b;
    
    // 为每个用户生成调度方案
    for (int user_id = 0; user_id < M; user_id++) {
        vector<Request> requests;
        int remaining_samples = users[user_id].sample_count;
        int current_time = users[user_id].start_time;
        
        // 找到最优服务器（基于延迟和性能的综合考虑）
        int best_server = 0;
        int min_latency = INT_MAX;
        for (int s = 0; s < N; s++) {
            if (latency[s][user_id] < min_latency) {
                min_latency = latency[s][user_id];
                best_server = s;
            }
        }
        
        // 计算最大可用批次大小（受显存限制）
        int max_batchsize = (servers[best_server].memory - b) / a;
        max_batchsize = min(max_batchsize, 1000); // 题目约束
        max_batchsize = max(max_batchsize, 1);    // 至少为1
        
        // 贪心策略：尽可能使用大批次以提高效率
        while (remaining_samples > 0) {
            int batchsize = min(remaining_samples, max_batchsize);
            
            // 选择NPU（简单策略：轮询）
            int npu = (requests.size() % servers[best_server].npus) + 1;
            
            Request req;
            req.time = current_time;
            req.server = best_server + 1; // 1-indexed
            req.npu = npu;
            req.batchsize = batchsize;
            
            requests.push_back(req);
            remaining_samples -= batchsize;
            
            // 计算下次发送时间（考虑通信延迟）
            current_time += latency[best_server][user_id] + 1;
            
            // 确保不超过用户的时间窗口
            if (current_time >= users[user_id].end_time && remaining_samples > 0) {
                // 如果时间不够，调整策略：减小批次大小或选择更快的服务器
                if (batchsize > 1) {
                    // 回退并使用更小的批次
                    requests.pop_back();
                    remaining_samples += batchsize;
                    current_time -= (latency[best_server][user_id] + 1);
                    
                    // 使用更小的批次大小
                    max_batchsize = max(1, max_batchsize / 2);
                    continue;
                }
                break;
            }
        }
        
        // 输出该用户的调度方案
        cout << requests.size() << "\n";
        for (const auto& req : requests) {
            cout << req.time << " " << req.server << " " << req.npu << " " << req.batchsize;
            if (&req != &requests.back()) cout << " ";
        }
        cout << "\n";
    }
    
    return 0;
}

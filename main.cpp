#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>

using namespace std;

struct Server {
    int npus;
    int speed_coeff;
    int memory;
};

struct User {
    int start_time;
    int end_time;
    int sample_count;
};

struct Request {
    int time;
    int server;
    int npu;
    int batchsize;
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    
    int N;
    cin >> N;
    
    vector<Server> servers(N);
    for (int i = 0; i < N; i++) {
        cin >> servers[i].npus >> servers[i].speed_coeff >> servers[i].memory;
    }
    
    int M;
    cin >> M;
    
    vector<User> users(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].start_time >> users[i].end_time >> users[i].sample_count;
    }
    
    vector<vector<int>> latency(N, vector<int>(M));
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> latency[i][j];
        }
    }
    
    int a, b;
    cin >> a >> b;
    
    // 为每个用户生成调度方案
    for (int user_id = 0; user_id < M; user_id++) {
        vector<Request> requests;
        
        int remaining_samples = users[user_id].sample_count;
        int current_time = users[user_id].start_time;
        
        // 选择最优服务器（基于延迟和性能的简单启发式）
        int best_server = 0;
        int min_latency = latency[0][user_id];
        for (int s = 1; s < N; s++) {
            if (latency[s][user_id] < min_latency) {
                min_latency = latency[s][user_id];
                best_server = s;
            }
        }
        
        // 计算最大可用batchsize（受显存限制）
        int max_batchsize = (servers[best_server].memory - b) / a;
        max_batchsize = min(max_batchsize, 1000); // 题目约束
        max_batchsize = max(max_batchsize, 1); // 至少为1
        
        // 使用第一个NPU（简化策略）
        int selected_npu = 1;
        
        // 分批处理样本
        while (remaining_samples > 0) {
            int batch_size = min(remaining_samples, max_batchsize);
            
            // 确保不超过用户时间窗口
            int available_time = users[user_id].end_time - current_time;
            if (available_time <= latency[best_server][user_id]) {
                // 时间不够，减少batch size
                batch_size = min(batch_size, remaining_samples);
            }
            
            requests.push_back({
                current_time,
                best_server + 1, // 1-indexed
                selected_npu,
                batch_size
            });
            
            remaining_samples -= batch_size;
            
            // 更新下次发送时间（考虑通信延迟）
            current_time += latency[best_server][user_id] + 1;
            
            // 防止无限循环
            if (requests.size() >= 300) break;
        }
        
        // 输出结果
        cout << requests.size() << "\n";
        for (const auto& req : requests) {
            cout << req.time << " " << req.server << " " << req.npu << " " << req.batchsize;
            if (&req != &requests.back()) cout << " ";
        }
        cout << "\n";
    }
    
    return 0;
}
